' ========================================
' Template de ObjectKey Manager
' Autor: Sistema ADHosp
' Data: 2025-01-14
' Descrição: Define o ObjectKey do manager da conta AD
' ========================================

Dim logMessage As String = ""

Try
    logMessage = String.Format("[{0}] Iniciando definição de ObjectKey Manager", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
    Console.WriteLine(logMessage)

    If $uid_person:string$.length() > 0 then
        Dim managerUID As String = $FK(UID_Person).UID_PersonHead$

        logMessage = String.Format("[{0}] UID da pessoa: {1}, Manager UID: {2}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), $uid_person:string$, managerUID)
        Console.WriteLine(logMessage)

        If Not String.IsNullOrEmpty(managerUID) Then
            Value = CCC_TPL_ADSAccount_ObjectkeyManager(managerUID)

            logMessage = String.Format("[{0}] ObjectKey Manager definido: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), Value)
            Console.WriteLine(logMessage)
        Else
            logMessage = String.Format("[{0}] AVISO: Manager UID não definido, campo será deixado vazio", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
        End If
    Else
        logMessage = String.Format("[{0}] AVISO: UID da pessoa não definido", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
        Console.WriteLine(logMessage)
    End If

Catch ex As Exception
    logMessage = String.Format("[{0}] ERRO: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
    Console.WriteLine(logMessage)
End Try

logMessage = String.Format("[{0}] Processamento de ObjectKey Manager finalizado", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
Console.WriteLine(logMessage)