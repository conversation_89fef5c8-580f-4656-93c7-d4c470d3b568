' ========================================
' Função de Gerenciamento de ObjectKey do Manager
' Autor: Sistema ADHosp
' Data: 2025-01-14
' Descrição: Localiza e retorna a chave do objeto do gerente da conta AD
' Implementa busca por domínio específico com fallback
' ========================================

Function CCC_tpl_ADSAccount_ObjectkeyManager(strUIDLineManager As String) As String
      Dim logMessage As String = ""
      Dim strResult As String = ""

      Try
        logMessage = String.Format("[{0}] Iniciando busca de ObjectKey do Manager para UID: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strUIDLineManager)
        Console.WriteLine(logMessage)

        '-> Only if there is a Line Manager on person configured
        If Not String.IsNullOrEmpty(strUIDLineManager) Then
          logMessage = String.Format("[{0}] Line Manager configurado, iniciando busca", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
          Console.WriteLine(logMessage)

          '-> Get the domain of the current account to find manager account in the same domain
          Dim strCurrentAccountDomain As String = ""

          logMessage = String.Format("[{0}] Obtendo domínio da conta atual...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
          Console.WriteLine(logMessage)

          ' Try multiple ways to get the current account's domain
          ' Method 1: From Variables (template context)
          Try
            logMessage = String.Format("[{0}] Método 1: Tentando obter domínio das Variables", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)

            Dim domainValue As Object = Variables("UID_ADSDomain")
            If domainValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(domainValue.ToString()) Then
              strCurrentAccountDomain = domainValue.ToString()

              logMessage = String.Format("[{0}] Domínio obtido das Variables: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strCurrentAccountDomain)
              Console.WriteLine(logMessage)
            End If
          Catch varEx As Exception
            logMessage = String.Format("[{0}] Método 1 falhou: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), varEx.Message)
            Console.WriteLine(logMessage)
            ' Continue to next method
          End Try

          ' Method 2: From Entity (if available) - only if not found yet
          If String.IsNullOrEmpty(strCurrentAccountDomain) Then
            Try
              logMessage = String.Format("[{0}] Método 2: Tentando obter domínio da Entity", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
              Console.WriteLine(logMessage)

              Dim entityValue As Object = Entity.GetValue("UID_ADSDomain")
              If entityValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(entityValue.ToString()) Then
                strCurrentAccountDomain = entityValue.ToString()

                logMessage = String.Format("[{0}] Domínio obtido da Entity: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strCurrentAccountDomain)
                Console.WriteLine(logMessage)
              End If
            Catch entityEx As Exception
              logMessage = String.Format("[{0}] Método 2 falhou: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), entityEx.Message)
              Console.WriteLine(logMessage)
              ' Continue to next method
            End Try
          End If

          ' Method 3: From current person's existing AD accounts - only if not found yet
          If String.IsNullOrEmpty(strCurrentAccountDomain) Then
            Try
              logMessage = String.Format("[{0}] Método 3: Tentando obter domínio das contas AD existentes", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
              Console.WriteLine(logMessage)

              Dim personValue As Object = Variables("UID_Person")
              If personValue IsNot Nothing AndAlso Not String.IsNullOrEmpty(personValue.ToString()) Then
                Dim strCurrentPerson As String = personValue.ToString()

                logMessage = String.Format("[{0}] Consultando contas AD para pessoa: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strCurrentPerson)
                Console.WriteLine(logMessage)

                Dim qCurrentDomain As Query = Query.From("ADSAccount") _
                                    .Where(String.Format("uid_person = '{0}'", strCurrentPerson)) _
                                    .Select("UID_ADSDomain")
                Dim colCurrentDomain As IEntityCollection = Session.Source.GetCollection(qCurrentDomain)
                If colCurrentDomain.Count > 0 Then
                  strCurrentAccountDomain = colCurrentDomain(0).GetValue("UID_ADSDomain").String

                  logMessage = String.Format("[{0}] Domínio obtido das contas existentes: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strCurrentAccountDomain)
                  Console.WriteLine(logMessage)
                Else
                  logMessage = String.Format("[{0}] Nenhuma conta AD encontrada para a pessoa", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                  Console.WriteLine(logMessage)
                End If
              End If
            Catch personEx As Exception
              logMessage = String.Format("[{0}] Método 3 falhou: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), personEx.Message)
              Console.WriteLine(logMessage)
              ' If all methods fail, strCurrentAccountDomain remains empty
              ' and we'll use fallback logic
            End Try
          End If

          If String.IsNullOrEmpty(strCurrentAccountDomain) Then
            logMessage = String.Format("[{0}] AVISO: Domínio da conta atual não pôde ser determinado, usando lógica de fallback", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
          End If

          '-> Account Definitions for standard users
          logMessage = String.Format("[{0}] Configurando definições de conta...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
          Console.WriteLine(logMessage)

          Const StdUsr_Account_Def As String = "AD-User"
          Dim StdUsr_Account_Def_terceiro As String = ""
          Try
            StdUsr_Account_Def_terceiro = Connection.GetConfigParm("Custom\AD\AccountDefforTerceiro").ToString()

            logMessage = String.Format("[{0}] Account Definition para terceiros: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), StdUsr_Account_Def_terceiro)
            Console.WriteLine(logMessage)
          Catch configEx As Exception
            logMessage = String.Format("[{0}] AVISO: Não foi possível obter AccountDefforTerceiro: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), configEx.Message)
            Console.WriteLine(logMessage)
            StdUsr_Account_Def_terceiro = ""
          End Try

          Dim ColADAccounts As IEntityCollection
          Dim qADSAccount As Query

          '-> Build the query based on whether we have domain information
          If Not String.IsNullOrEmpty(strCurrentAccountDomain) Then
            logMessage = String.Format("[{0}] MÉTODO PREFERIDO: Buscando conta do manager no mesmo domínio: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strCurrentAccountDomain)
            Console.WriteLine(logMessage)

            '-> PREFERRED: Search for manager account in the same domain with matching Account Definition
            '-> This ensures we get the manager's account from the same AD domain as the current account
            '-> Also check if the domain has a specific default Account Definition
            Dim domainSpecificQuery As String = String.Format("uid_person = '{0}' and UID_ADSDomain = '{1}'", strUIDLineManager, strCurrentAccountDomain)

            '-> First try with the standard Account Definitions
            Dim accountDefFilter As String = String.Format("UID_TSBAccountDef in (select uid_TSBAccountDef from TSBAccountDef where Ident_TSBAccountDef in ('{0}','{1}'))", StdUsr_Account_Def, StdUsr_Account_Def_terceiro)

            '-> Also include the domain's default Account Definition if it exists
            Dim domainDefaultAccountDef As String = String.Format("UID_TSBAccountDef in (select UID_TSBAccountDef from ADSDomain where UID_ADSDomain = '{0}' and UID_TSBAccountDef is not null)", strCurrentAccountDomain)

            '-> Combine the filters with OR logic
            Dim combinedAccountDefFilter As String = String.Format("({0} OR {1})", accountDefFilter, domainDefaultAccountDef)

            logMessage = String.Format("[{0}] Query construída para busca específica por domínio", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)

            qADSAccount = Query.From("ADSAccount") _
                          .Where(String.Format("{0} and ({1})", domainSpecificQuery, combinedAccountDefFilter)) _
                          .Select("xobjectkey")
          Else
            logMessage = String.Format("[{0}] MÉTODO FALLBACK: Buscando todas as contas do manager", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)

            '-> FALLBACK: If current domain is unknown, get all manager accounts with domain info
            '-> We'll then try to match domains or use the first available account
            qADSAccount = Query.From("ADSAccount") _
                          .Where(String.Format("uid_person = '{0}' and UID_TSBAccountDef in (select uid_TSBAccountDef from TSBAccountDef where Ident_TSBAccountDef in ('{1}','{2}'))", _
                                 strUIDLineManager, StdUsr_Account_Def, StdUsr_Account_Def_terceiro)) _
                          .Select("xobjectkey", "UID_ADSDomain")
          End If

          logMessage = String.Format("[{0}] Executando consulta na base de dados...", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
          Console.WriteLine(logMessage)

          ColADAccounts = Session.Source.GetCollection(qADSAccount)

          logMessage = String.Format("[{0}] Consulta executada - {1} conta(s) encontrada(s)", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ColADAccounts.Count)
          Console.WriteLine(logMessage)

          '-> Process the results to find the best manager account
          If ColADAccounts.Count > 0 Then
            If Not String.IsNullOrEmpty(strCurrentAccountDomain) Then
              '-> Domain-specific search was successful, use the result
              strResult = ColADAccounts(0).GetValue("xobjectkey").String

              logMessage = String.Format("[{0}] SUCESSO: ObjectKey do manager encontrado (busca específica): {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strResult)
              Console.WriteLine(logMessage)
            Else
              '-> Fallback processing: we have manager accounts but don't know current domain
              '-> Use the first available manager account
              '-> TODO: Could be enhanced to prefer accounts from default domain or use additional logic
              strResult = ColADAccounts(0).GetValue("xobjectkey").String

              logMessage = String.Format("[{0}] SUCESSO: ObjectKey do manager encontrado (fallback): {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), strResult)
              Console.WriteLine(logMessage)
            End If
          Else
            logMessage = String.Format("[{0}] AVISO: Nenhuma conta do manager encontrada", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
            Console.WriteLine(logMessage)
          End If
          '-> If no manager account found (either in same domain or at all), strResult remains empty
          '-> This satisfies the requirement to leave the field empty when no manager account is found
        Else
          logMessage = String.Format("[{0}] Line Manager não configurado, campo será deixado vazio", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
          Console.WriteLine(logMessage)
        End If

      Catch ex As Exception
        logMessage = String.Format("[{0}] ERRO CRÍTICO: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message)
        Console.WriteLine(logMessage)

        Throw New Exception("Error in script CCC_tpl_ADSAccount_ObjectkeyManager: " & ex.Message, ex)

      Finally
        logMessage = String.Format("[{0}] Finalizando busca de ObjectKey - Resultado: {1}", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), If(String.IsNullOrEmpty(strResult), "VAZIO", strResult))
        Console.WriteLine(logMessage)

        CCC_tpl_ADSAccount_ObjectkeyManager = strResult

      End Try

End Function